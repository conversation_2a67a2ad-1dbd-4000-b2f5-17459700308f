import psycopg2
import pandas as pd
from dash import Dash, dcc, html
from dash.dependencies import Input, Output
import plotly.graph_objs as go
from datetime import datetime
import warnings
import sqlalchemy
import urllib.parse
import numpy as np
from sklearn.preprocessing import MinMaxScaler, StandardScaler

# Suppress warnings
warnings.filterwarnings("ignore")

# 🔧 PostgreSQL credentials
DB_NAME = "eq_DB"
DB_USER = "postgres"
DB_PASS = "Muni@555"
DB_HOST = "localhost"
DB_PORT = "5432"

# 🔧 Symbols and styling
SYMBOLS = ["RELIANCE-EQ", "NIFTY INDEX", "INDIA VIX"]

# Enhanced color palette for better line distinction
SYMBOL_COLORS = {
    "RELIANCE-EQ": ["#1f77b4", "#aec7e8", "#ff7f0e", "#ffbb78", "#2ca02c", "#98df8a"],
    "NIFTY INDEX": ["#d62728", "#ff9896", "#9467bd", "#c5b0d5", "#8c564b", "#c49c94"],
    "INDIA VIX": ["#e377c2", "#f7b6d3", "#7f7f7f", "#c7c7c7", "#bcbd22", "#dbdb8d"]
}

DASH_STYLES = ['solid', 'dash', 'dot', 'dashdot', 'longdash', 'longdashdot']

price_metrics = ['lp', 'o', 'h', 'l', 'c', 'ap']
quantity_metrics = ['ltq', 'tbq', 'tsq', 'tbuyq', 'tsellq']

metric_display = {
    'lp': 'LP',
    'o': 'Open',
    'h': 'High',
    'l': 'Low',
    'c': 'Prev Close',
    'ap': 'Avg Price',
    'ltq': 'LTQ',
    'tbq': 'TBQ',
    'tsq': 'TSQ',
    'tbuyq': 'TBuyQ',
    'tsellq': 'TSellQ'
}

# This mapping is no longer needed as we generate y-axes dynamically

def fetch_data():
    import urllib.parse
    # URL-encode the password to handle special characters
    encoded_password = urllib.parse.quote_plus(DB_PASS)
    engine = sqlalchemy.create_engine(f'postgresql://{DB_USER}:{encoded_password}@{DB_HOST}:{DB_PORT}/{DB_NAME}')
    
    metrics_keys = list(metric_display.keys())
    select_parts = [f"raw_json::json->>'{key}' AS {key}" for key in metrics_keys]
    query = f"""
        SELECT symbol, request_time, {', '.join(select_parts)}
        FROM quote_snapshots
        WHERE symbol IN %(symbols)s
        ORDER BY request_time ASC
    """
    df = pd.read_sql(query, engine, params={"symbols": tuple(SYMBOLS)})
    
    for col in metrics_keys:
        df[col] = pd.to_numeric(df[col], errors="coerce")
    df["request_time"] = pd.to_datetime(df["request_time"])
    engine.dispose()

    # Log missing symbols
    missing = [s for s in SYMBOLS if s not in df["symbol"].unique()]
    if missing:
        print(f"⚠️ No data found for: {', '.join(missing)}")

    return df

app = Dash(__name__)
app.title = "📈 Multi-Y LTP Dashboard"

timeframe_options = [
    {"label": "30 Seconds", "value": "30S"},
    {"label": "1 Minute", "value": "1T"},
    {"label": "5 Minutes", "value": "5T"},
    {"label": "15 Minutes", "value": "15T"},
    {"label": "75 Minutes", "value": "75T"},
    {"label": "Daily", "value": "D"}
]

normalization_options = [
    {"label": "No Normalization", "value": "none"},
    {"label": "Min-Max Scaling (0-1)", "value": "minmax"},
    {"label": "Standard Scaling (Z-score)", "value": "standard"},
    {"label": "Percentage Change", "value": "pct_change"}
]

app.layout = html.Div([
    html.H2("📊 Enhanced Multi-Axis Chart with Normalization"),

    html.Div([
        html.Div([
            html.Label("Normalization Method:"),
            dcc.Dropdown(
                id="normalization-dropdown",
                options=normalization_options,
                value="none",
                style={"width": "100%", "marginBottom": "10px"}
            ),
        ], style={"width": "30%", "display": "inline-block", "marginRight": "5%"}),

        html.Div([
            html.Label("Select Timeframe:"),
            dcc.Dropdown(
                id="timeframe-dropdown",
                options=timeframe_options,
                value="1T",
                style={"width": "100%", "marginBottom": "10px"}
            ),
        ], style={"width": "30%", "display": "inline-block", "marginRight": "5%"}),

        html.Div([
            dcc.Checklist(
                id="toggle-yaxis",
                options=[{"label": "Hide Y-axes", "value": "hide"}],
                value=[],
                style={"marginTop": "25px"}
            ),
        ], style={"width": "25%", "display": "inline-block"}),
    ], style={"marginBottom": "20px"}),

    html.Div([
        html.Label("Select Metrics to Display:"),
        dcc.Checklist(
            id="metrics-selector",
            options=[{"label": metric_display[key], "value": key} for key in metric_display.keys()],
            value=list(metric_display.keys()),
            inline=True,
            style={"marginBottom": "20px"}
        ),
    ]),

    dcc.Graph(
        id="ltp-chart",
        config={'scrollZoom': True, 'displayModeBar': True}
    ),
    dcc.Interval(id="interval", interval=10*1000, n_intervals=0),
])

def normalize_data(data, method):
    """Apply normalization to data series"""
    if method == "none" or data.empty:
        return data
    elif method == "minmax":
        scaler = MinMaxScaler()
        return pd.Series(scaler.fit_transform(data.values.reshape(-1, 1)).flatten(), index=data.index)
    elif method == "standard":
        scaler = StandardScaler()
        return pd.Series(scaler.fit_transform(data.values.reshape(-1, 1)).flatten(), index=data.index)
    elif method == "pct_change":
        return data.pct_change().fillna(0)
    return data

@app.callback(
    Output("ltp-chart", "figure"),
    [Input("interval", "n_intervals"),
     Input("toggle-yaxis", "value"),
     Input("timeframe-dropdown", "value"),
     Input("normalization-dropdown", "value"),
     Input("metrics-selector", "value")]
)
def update_chart(n, toggle, timeframe, normalization, selected_metrics):
    df = fetch_data()
    fig = go.Figure()
    show_yaxis = "hide" not in toggle
    annotations = []

    # Filter selected metrics
    if not selected_metrics:
        selected_metrics = list(metric_display.keys())

    # Create dynamic y-axis assignments
    y_axis_counter = 1
    metric_yaxis_map = {}

    # Assign separate y-axes for each metric-symbol combination
    for symbol in SYMBOLS:
        for metric in selected_metrics:
            if metric in metric_display:
                key = f"{symbol}_{metric}"
                if y_axis_counter == 1:
                    metric_yaxis_map[key] = "y"
                else:
                    metric_yaxis_map[key] = f"y{y_axis_counter}"
                y_axis_counter += 1

    for i, symbol in enumerate(SYMBOLS):
        sub_original = df[df["symbol"] == symbol]
        if sub_original.empty:
            continue

        symbol_colors = SYMBOL_COLORS[symbol]
        color_idx = 0

        # Process all selected metrics for this symbol
        for mkey in selected_metrics:
            if mkey in sub_original.columns and mkey in metric_display:
                mname = metric_display[mkey]
                sub_timeseries = sub_original.set_index("request_time")[mkey].sort_index().resample(timeframe).last().dropna()

                if not sub_timeseries.empty:
                    # Apply normalization
                    normalized_series = normalize_data(sub_timeseries, normalization)

                    sub = pd.DataFrame({
                        "request_time": normalized_series.index,
                        "value": normalized_series.values
                    })

                    # Get unique color and dash style for this line
                    line_color = symbol_colors[color_idx % len(symbol_colors)]
                    dash_idx = list(metric_display.keys()).index(mkey)
                    dash_style = DASH_STYLES[dash_idx % len(DASH_STYLES)]

                    # Get y-axis for this metric-symbol combination
                    yaxis_key = f"{symbol}_{mkey}"
                    yaxis_ref = metric_yaxis_map.get(yaxis_key, "y")

                    fig.add_trace(go.Scatter(
                        x=sub["request_time"],
                        y=sub["value"],
                        mode="lines+markers",
                        name=f"{symbol} {mname}",
                        line=dict(color=line_color, dash=dash_style, width=2),
                        marker=dict(size=4),
                        yaxis=yaxis_ref
                    ))

                    # Add value annotation
                    last_x = sub["request_time"].iloc[-1]
                    last_y = sub["value"].iloc[-1]
                    if pd.notna(last_y):
                        ann = dict(
                            x=last_x,
                            y=last_y,
                            xref="x",
                            yref=yaxis_ref,
                            text=f"{last_y:.2f}",
                            showarrow=False,
                            xanchor="left",
                            yanchor="middle",
                            font=dict(color=line_color, size=9),
                            bgcolor="rgba(0,0,0,0.8)",
                            bordercolor=line_color,
                            borderwidth=1
                        )
                        annotations.append(ann)

                    color_idx += 1
    # Create layout with dynamic y-axes
    layout = dict(
        xaxis=dict(title="Time", showgrid=True),
        legend=dict(
            title="Metrics",
            orientation="v",
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.02
        ),
        template="plotly_dark",
        height=800,
        dragmode='zoom',
        uirevision="constant",  # Preserve zoom/pan state
        margin=dict(r=200)  # Extra margin for legend
    )

    # Configure dynamic y-axes
    axis_positions = {"left": 0, "right": 1}
    left_offset = 0
    right_offset = 0

    for key, yaxis_ref in metric_yaxis_map.items():
        symbol, metric = key.split("_", 1)
        metric_name = metric_display.get(metric, metric)

        # Determine side based on metric type
        if metric in price_metrics:
            side = "left"
            position = axis_positions["left"] - left_offset * 0.08
            left_offset += 1
        else:
            side = "right"
            position = axis_positions["right"] + right_offset * 0.08
            right_offset += 1

        # Configure y-axis
        yaxis_layout_key = "yaxis" if yaxis_ref == "y" else f"yaxis{yaxis_ref[1:]}"

        overlay = None if yaxis_ref == "y" else "y"

        title_text = f"{symbol} {metric_name}" if show_yaxis else ""
        if normalization != "none":
            title_text += f" ({normalization})" if show_yaxis else ""

        layout[yaxis_layout_key] = dict(
            title=title_text,
            overlaying=overlay,
            side=side,
            position=max(0, min(1, position)),  # Clamp between 0 and 1
            showgrid=False,
            showticklabels=show_yaxis,
            zeroline=False
        )

    layout["annotations"] = annotations
    fig.update_layout(**layout)
    return fig

if __name__ == "__main__":
    app.run_server(debug=True)
import psycopg2
import pandas as pd
from dash import Dash, dcc, html
from dash.dependencies import Input, Output
import plotly.graph_objs as go
from datetime import datetime
import warnings
import sqlalchemy
import urllib.parse

# Suppress warnings
warnings.filterwarnings("ignore")

# 🔧 PostgreSQL credentials
DB_NAME = "eq_DB"
DB_USER = "postgres"
DB_PASS = "Muni@555"
DB_HOST = "localhost"
DB_PORT = "5432"

# 🔧 Symbols and styling
SYMBOLS = ["RELIANCE-EQ", "NIFTY INDEX", "INDIA VIX"]
COLORS = {"RELIANCE-EQ": "#1f77b4", "NIFTY INDEX": "#ff7f0e", "INDIA VIX": "#2ca02c"}
DASH_STYLES = ['solid', 'dash', 'dot', 'dashdot', 'longdash', 'longdashdot']

price_metrics = ['lp', 'o', 'h', 'l', 'c', 'ap']
quantity_metrics = ['ltq', 'tbq', 'tsq', 'tbuyq', 'tsellq']

metric_display = {
    'lp': 'LP',
    'o': 'Open',
    'h': 'High',
    'l': 'Low',
    'c': 'Prev Close',
    'ap': 'Avg Price',
    'ltq': 'LTQ',
    'tbq': 'TBQ',
    'tsq': 'TSQ',
    'tbuyq': 'TBuyQ',
    'tsellq': 'TSellQ'
}

yaxis_mapping = {
    'y': 'yaxis',
    'y2': 'yaxis2',
    'y3': 'yaxis3',
    'y4': 'yaxis4',
    'y5': 'yaxis5',
    'y6': 'yaxis6'
}

def fetch_data():
    import urllib.parse
    # URL-encode the password to handle special characters
    encoded_password = urllib.parse.quote_plus(DB_PASS)
    engine = sqlalchemy.create_engine(f'postgresql://{DB_USER}:{encoded_password}@{DB_HOST}:{DB_PORT}/{DB_NAME}')
    
    metrics_keys = list(metric_display.keys())
    select_parts = [f"raw_json::json->>'{key}' AS {key}" for key in metrics_keys]
    query = f"""
        SELECT symbol, request_time, {', '.join(select_parts)}
        FROM quote_snapshots
        WHERE symbol IN %(symbols)s
        ORDER BY request_time ASC
    """
    df = pd.read_sql(query, engine, params={"symbols": tuple(SYMBOLS)})
    
    for col in metrics_keys:
        df[col] = pd.to_numeric(df[col], errors="coerce")
    df["request_time"] = pd.to_datetime(df["request_time"])
    engine.dispose()

    # Log missing symbols
    missing = [s for s in SYMBOLS if s not in df["symbol"].unique()]
    if missing:
        print(f"⚠️ No data found for: {', '.join(missing)}")

    return df

app = Dash(__name__)
app.title = "📈 Multi-Y LTP Dashboard"

timeframe_options = [
    {"label": "30 Seconds", "value": "30S"},
    {"label": "1 Minute", "value": "1T"},
    {"label": "5 Minutes", "value": "5T"},
    {"label": "15 Minutes", "value": "15T"},
    {"label": "75 Minutes", "value": "75T"},
    {"label": "Daily", "value": "D"}
]

app.layout = html.Div([
    html.H2("📊 Historical LTP Tracker with Parallel Y-Axes"),
    dcc.Checklist(
        id="toggle-yaxis",
        options=[{"label": "Hide all Y-axes", "value": "hide"}],
        value=[],
        style={"marginBottom": "20px"}
    ),
    html.Label("Select Timeframe:"),
    dcc.Dropdown(
        id="timeframe-dropdown",
        options=timeframe_options,
        value="1T",
        style={"width": "50%", "marginBottom": "20px"}
    ),
    dcc.Graph(
        id="ltp-chart",
        config={'scrollZoom': True, 'displayModeBar': True}
    ),
    dcc.Interval(id="interval", interval=10*1000, n_intervals=0),
])

@app.callback(
    Output("ltp-chart", "figure"),
    [Input("interval", "n_intervals"),
     Input("toggle-yaxis", "value"),
     Input("timeframe-dropdown", "value")]
)
def update_chart(n, toggle, timeframe):
    df = fetch_data()
    fig = go.Figure()
    show_yaxis = "hide" not in toggle
    annotations = []

    price_yaxes = ['y', 'y3', 'y5']
    quant_yaxes = ['y2', 'y4', 'y6']

    metric_order = list(metric_display.keys())

    for i, symbol in enumerate(SYMBOLS):
        sub_original = df[df["symbol"] == symbol]
        if sub_original.empty:
            continue
        
        y_price = price_yaxes[i]
        y_quant = quant_yaxes[i]
        
        # Price metrics
        for mkey in price_metrics:
            if mkey in sub_original.columns and mkey in metric_display:
                mname = metric_display[mkey]
                sub_timeseries = sub_original.set_index("request_time")[mkey].sort_index().resample(timeframe).last().dropna()
                if not sub_timeseries.empty:
                    sub = pd.DataFrame({
                        "request_time": sub_timeseries.index,
                        "value": sub_timeseries.values
                    })
                    
                    dash_idx = metric_order.index(mkey)
                    dash_style = DASH_STYLES[dash_idx % len(DASH_STYLES)]
                    
                    fig.add_trace(go.Scatter(
                        x=sub["request_time"],
                        y=sub["value"],
                        mode="lines+markers",
                        name=f"{symbol} {mname}",
                        line=dict(color=COLORS[symbol], dash=dash_style),
                        yaxis=y_price
                    ))
                    
                    last_x = sub["request_time"].iloc[-1]
                    last_y = sub["value"].iloc[-1]
                    if pd.notna(last_y):
                        ann = dict(
                            x=last_x,
                            y=last_y,
                            xref="x",
                            yref=y_price,
                            text=f"{last_y:.2f}",
                            showarrow=False,
                            xanchor="left",
                            yanchor="top",
                            font=dict(color=COLORS[symbol], size=10),
                            bgcolor="rgba(0,0,0,0.7)",
                            bordercolor=COLORS[symbol],
                            borderwidth=1
                        )
                        annotations.append(ann)
        
        # Quantity metrics
        for mkey in quantity_metrics:
            if mkey in sub_original.columns and mkey in metric_display:
                mname = metric_display[mkey]
                sub_timeseries = sub_original.set_index("request_time")[mkey].sort_index().resample(timeframe).last().dropna()
                if not sub_timeseries.empty:
                    sub = pd.DataFrame({
                        "request_time": sub_timeseries.index,
                        "value": sub_timeseries.values
                    })
                    
                    dash_idx = metric_order.index(mkey)
                    dash_style = DASH_STYLES[dash_idx % len(DASH_STYLES)]
                    
                    fig.add_trace(go.Scatter(
                        x=sub["request_time"],
                        y=sub["value"],
                        mode="lines+markers",
                        name=f"{symbol} {mname}",
                        line=dict(color=COLORS[symbol], dash=dash_style),
                        yaxis=y_quant
                    ))
                    
                    last_x = sub["request_time"].iloc[-1]
                    last_y = sub["value"].iloc[-1]
                    if pd.notna(last_y):
                        ann = dict(
                            x=last_x,
                            y=last_y,
                            xref="x",
                            yref=y_quant,
                            text=f"{last_y:.0f}",
                            showarrow=False,
                            xanchor="left",
                            yanchor="top",
                            font=dict(color=COLORS[symbol], size=10),
                            bgcolor="rgba(0,0,0,0.7)",
                            bordercolor=COLORS[symbol],
                            borderwidth=1
                        )
                        annotations.append(ann)

    layout = dict(
        xaxis=dict(title="Time"),
        legend=dict(title="Symbol"),
        template="plotly_dark",
        height=800,
        dragmode='zoom',
        uirevision="constant"  # Preserve zoom/pan state
    )

    # Configure price y-axes (left)
    for i, symbol in enumerate(SYMBOLS):
        y_name_trace = price_yaxes[i]
        y_name_layout = yaxis_mapping[y_name_trace]
        overlay = None if i == 0 else 'y'
        layout[y_name_layout] = dict(
            title=f"{symbol} Price" if show_yaxis else "",
            overlaying=overlay,
            side="left",
            position=0,
            showgrid=False,
            showticklabels=show_yaxis
        )
    
    # Configure quantity y-axes (right)
    for i, symbol in enumerate(SYMBOLS):
        y_name_trace = quant_yaxes[i]
        y_name_layout = yaxis_mapping[y_name_trace]
        overlay = None if i == 0 else 'y2'
        position_right = 1.0 - i * 0.05
        layout[y_name_layout] = dict(
            title=f"{symbol} Quant" if show_yaxis else "",
            overlaying=overlay,
            side="right",
            position=position_right,
            showgrid=False,
            showticklabels=show_yaxis
        )
    
    layout["annotations"] = annotations

    fig.update_layout(**layout)
    return fig

if __name__ == "__main__":
    app.run_server(debug=True)
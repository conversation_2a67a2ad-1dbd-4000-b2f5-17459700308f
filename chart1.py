import psycopg2
import pandas as pd
from dash import Dash, dcc, html
from dash.dependencies import Input, Output
import plotly.graph_objs as go
from datetime import datetime
import warnings
import sqlalchemy
import urllib.parse

# Suppress warnings
warnings.filterwarnings("ignore")

# 🔧 PostgreSQL credentials
DB_NAME = "eq_DB"
DB_USER = "postgres"
DB_PASS = "Muni@555"
DB_HOST = "localhost"
DB_PORT = "5432"

# 🔧 Symbols and styling
SYMBOLS = ["RELIANCE-EQ", "NIFTY INDEX", "INDIA VIX"]
COLORS = {"RELIANCE-EQ": "#1f77b4", "NIFTY INDEX": "#ff7f0e", "INDIA VIX": "#2ca02c"}
STYLES = {"RELIANCE-EQ": "solid", "NIFTY INDEX": "solid", "INDIA VIX": "dot"}

def fetch_data():
    import urllib.parse
    # URL-encode the password to handle special characters
    encoded_password = urllib.parse.quote_plus(DB_PASS)
    engine = sqlalchemy.create_engine(f'postgresql://{DB_USER}:{encoded_password}@{DB_HOST}:{DB_PORT}/{DB_NAME}')
    
    query = """
        SELECT symbol, request_time, raw_json::json->>'lp' AS ltp
        FROM quote_snapshots
        WHERE symbol IN %(symbols)s
        ORDER BY request_time ASC
    """
    df = pd.read_sql(query, engine, params={"symbols": tuple(SYMBOLS)})
    df["ltp"] = pd.to_numeric(df["ltp"], errors="coerce")
    df["request_time"] = pd.to_datetime(df["request_time"])
    engine.dispose()

    # Log missing symbols
    missing = [s for s in SYMBOLS if s not in df["symbol"].unique()]
    if missing:
        print(f"⚠️ No data found for: {', '.join(missing)}")

    return df

app = Dash(__name__)
app.title = "📈 Multi-Y LTP Dashboard"

timeframe_options = [
    {"label": "30 Seconds", "value": "30S"},
    {"label": "1 Minute", "value": "1T"},
    {"label": "5 Minutes", "value": "5T"},
    {"label": "15 Minutes", "value": "15T"},
    {"label": "75 Minutes", "value": "75T"},
    {"label": "Daily", "value": "D"}
]

app.layout = html.Div([
    html.H2("📊 Historical LTP Tracker with Parallel Y-Axes"),
    dcc.Checklist(
        id="toggle-yaxis",
        options=[{"label": "Hide all Y-axes", "value": "hide"}],
        value=[],
        style={"marginBottom": "20px"}
    ),
    html.Label("Select Timeframe:"),
    dcc.Dropdown(
        id="timeframe-dropdown",
        options=timeframe_options,
        value="1T",
        style={"width": "50%", "marginBottom": "20px"}
    ),
    dcc.Graph(id="ltp-chart"),
    dcc.Interval(id="interval", interval=10*1000, n_intervals=0),
])

@app.callback(
    Output("ltp-chart", "figure"),
    [Input("interval", "n_intervals"),
     Input("toggle-yaxis", "value"),
     Input("timeframe-dropdown", "value")]
)
def update_chart(n, toggle, timeframe):
    df = fetch_data()
    fig = go.Figure()
    show_yaxis = "hide" not in toggle
    annotations = []

    for i, symbol in enumerate(SYMBOLS):
        sub_original = df[df["symbol"] == symbol]
        if sub_original.empty:
            sub = pd.DataFrame({
                "request_time": [datetime.now()],
                "ltp": [None],
                "symbol": [symbol]
            })
        else:
            sub_timeseries = sub_original.set_index("request_time")["ltp"].sort_index().resample(timeframe).last().dropna()
            if sub_timeseries.empty:
                sub = pd.DataFrame({
                    "request_time": [datetime.now()],
                    "ltp": [None],
                    "symbol": [symbol]
                })
            else:
                sub = pd.DataFrame({
                    "request_time": sub_timeseries.index,
                    "ltp": sub_timeseries.values,
                    "symbol": [symbol] * len(sub_timeseries)
                })
        
        last_x = sub["request_time"].iloc[-1]
        last_y = sub["ltp"].iloc[-1] if not sub["ltp"].empty and pd.notna(sub["ltp"].iloc[-1]) else None
        
        yaxis_name = "y" if i == 0 else f"y{i+1}"
        
        fig.add_trace(go.Scatter(
            x=sub["request_time"],
            y=sub["ltp"],
            mode="lines+markers",
            name=symbol,
            line=dict(color=COLORS[symbol], dash=STYLES[symbol]),
            yaxis=yaxis_name
        ))
        
        if pd.notna(last_y):
            ann = dict(
                x=last_x,
                y=last_y,
                xref="x",
                yref=yaxis_name,
                text=f"{last_y:.2f}",
                showarrow=False,
                xanchor="left",
                yanchor="top",
                font=dict(color=COLORS[symbol], size=12),
                bgcolor="rgba(0,0,0,0.7)",
                bordercolor=COLORS[symbol],
                borderwidth=1
            )
            annotations.append(ann)

    layout = dict(
        xaxis=dict(title="Time"),
        legend=dict(title="Symbol"),
        template="plotly_dark",
        height=600,
        uirevision="constant"  # Preserve zoom/pan state
    )

    # Configure y-axes
    for i, symbol in enumerate(SYMBOLS):
        if i == 0:
            layout["yaxis"] = dict(
                title=symbol if show_yaxis else "",
                side="left",
                showgrid=False,
                showticklabels=show_yaxis
            )
        else:
            layout[f"yaxis{i+1}"] = dict(
                title=symbol if show_yaxis else "",
                overlaying="y",
                side="right",
                position=1.0 - (i-1) * 0.05,
                showgrid=False,
                showticklabels=show_yaxis
            )
    
    layout["annotations"] = annotations

    fig.update_layout(**layout)
    return fig

if __name__ == "__main__":
    app.run_server(debug=True)